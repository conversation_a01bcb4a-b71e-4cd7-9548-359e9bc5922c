{"name": "@emotion/hash", "version": "0.8.0", "description": "A MurmurHash2 implementation", "main": "dist/hash.cjs.js", "module": "dist/hash.esm.js", "types": "types/index.d.ts", "license": "MIT", "repository": "https://github.com/emotion-js/emotion/tree/master/packages/hash", "publishConfig": {"access": "public"}, "files": ["src", "dist", "types"], "scripts": {"test:typescript": "dtslint types"}, "devDependencies": {"dtslint": "^0.3.0"}, "browser": {"./dist/hash.cjs.js": "./dist/hash.browser.cjs.js", "./dist/hash.esm.js": "./dist/hash.browser.esm.js"}}