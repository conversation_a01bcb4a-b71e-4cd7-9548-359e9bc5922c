{"name": "@emotion/unitless", "version": "0.7.5", "description": "An object of css properties that don't accept values with units", "main": "dist/unitless.cjs.js", "module": "dist/unitless.esm.js", "license": "MIT", "repository": "https://github.com/emotion-js/emotion/tree/master/packages/unitless", "publishConfig": {"access": "public"}, "files": ["src", "dist"], "browser": {"./dist/unitless.cjs.js": "./dist/unitless.browser.cjs.js", "./dist/unitless.esm.js": "./dist/unitless.browser.esm.js"}}