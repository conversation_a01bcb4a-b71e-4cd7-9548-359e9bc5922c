{"name": "rc-tabs", "version": "15.7.0", "description": "tabs ui component for react", "keywords": ["react", "react-component", "react-tabs"], "homepage": "http://github.com/react-component/tabs", "bugs": {"url": "http://github.com/react-component/tabs/issues"}, "repository": {"type": "git", "url": "**************:react-component/tabs.git"}, "license": "MIT", "author": "<EMAIL>", "main": "./lib/index", "module": "./es/index", "files": ["lib", "es", "assets/index.css"], "scripts": {"build": "dumi build", "compile": "father build && npm run compile:style", "compile:style": "lessc --js assets/index.less assets/index.css", "coverage": "father test --coverage", "docs:deploy": "gh-pages -d .doc", "lint": "eslint src/ docs/examples/ --ext .tsx,.ts,.jsx,.js", "now-build": "npm run build", "prepublishOnly": "npm run lint && npm run test && npm run compile && np --yolo --no-publish --branch=antd-5.x", "start": "dumi dev", "test": "rc-test", "prepare": "husky"}, "dependencies": {"@babel/runtime": "^7.11.2", "classnames": "2.x", "rc-dropdown": "~4.2.0", "rc-menu": "~9.16.0", "rc-motion": "^2.6.2", "rc-resize-observer": "^1.0.0", "rc-util": "^5.34.1"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.0", "@rc-component/trigger": "^2.0.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/classnames": "^2.2.10", "@types/enzyme": "^3.10.5", "@types/jest": "^29.4.0", "@types/keyv": "4.2.0", "@types/react": "^18.2.42", "@types/react-dom": "^18.0.11", "@umijs/fabric": "^4.0.1", "coveralls": "^3.0.6", "cross-env": "^7.0.2", "dumi": "^2.0.0", "eslint": "^8.54.0", "eslint-plugin-jest": "^28.9.0", "eslint-plugin-unicorn": "^56.0.1", "fastclick": "~1.0.6", "father": "^4.0.0", "gh-pages": "^6.1.0", "history": "^5.3.0", "husky": "^9.1.7", "immutability-helper": "^3.0.1", "less": "^4.1.3", "lint-staged": "^15.5.1", "np": "^10.0.2", "preact-compat": "^3.16.0", "prettier": "^3.5.3", "rc-test": "^7.0.14", "react": "^18.0.0", "react-dnd": "^10.0.0", "react-dnd-html5-backend": "^10.0.0", "react-dom": "^18.0.0", "react-sticky": "^6.0.3", "sortablejs": "^1.7.0", "typescript": "^5.3.2"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "engines": {"node": ">=8.x"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}}