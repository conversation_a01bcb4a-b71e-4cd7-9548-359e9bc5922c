import * as React from 'react';
var AddButton = /*#__PURE__*/React.forwardRef(function (props, ref) {
  var prefixCls = props.prefixCls,
    editable = props.editable,
    locale = props.locale,
    style = props.style;
  if (!editable || editable.showAdd === false) {
    return null;
  }
  return /*#__PURE__*/React.createElement("button", {
    ref: ref,
    type: "button",
    className: "".concat(prefixCls, "-nav-add"),
    style: style,
    "aria-label": (locale === null || locale === void 0 ? void 0 : locale.addAriaLabel) || 'Add tab',
    onClick: function onClick(event) {
      editable.onEdit('add', {
        event: event
      });
    }
  }, editable.addIcon || '+');
});
export default AddButton;