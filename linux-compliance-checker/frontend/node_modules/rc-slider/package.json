{"name": "rc-slider", "version": "11.1.8", "description": "Slider UI component for React", "keywords": ["react", "react-component", "react-slider", "slider", "input", "range"], "homepage": "http://github.com/react-component/slider/", "bugs": {"url": "http://github.com/react-component/slider/issues"}, "repository": {"type": "git", "url": "**************:react-component/slider.git"}, "license": "MIT", "main": "./lib/index", "module": "./es/index", "types": "./lib/index.d.ts", "style": "./assets/index.css", "files": ["assets/*.css", "lib", "es"], "scripts": {"compile": "father build && lessc assets/index.less assets/index.css", "coverage": "rc-test --coverage", "docs:build": "dumi build", "docs:deploy": "gh-pages -d .doc", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "now-build": "npm run docs:build", "prepublishOnly": "npm run compile && np --yolo --no-publish", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "start": "dumi dev", "test": "rc-test"}, "dependencies": {"@babel/runtime": "^7.10.1", "classnames": "^2.2.5", "rc-util": "^5.36.0"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^12.1.3", "@types/classnames": "^2.2.9", "@types/jest": "^29.5.1", "@types/node": "^22.5.0", "@types/react": "^18.2.42", "@types/react-dom": "^18.0.11", "@umijs/fabric": "^4.0.1", "cross-env": "^7.0.0", "dumi": "^2.2.10", "eslint": "^8.54.0", "eslint-plugin-jest": "^28.2.0", "eslint-plugin-unicorn": "^54.0.0", "father": "^4.3.5", "father-build": "^1.18.6", "gh-pages": "^6.1.0", "glob": "^7.1.6", "less": "^4.1.3", "np": "^10.0.4", "rc-test": "^7.0.15", "rc-tooltip": "^6.1.2", "rc-trigger": "^5.3.4", "react": "^16.0.0", "react-dom": "^16.0.0", "regenerator-runtime": "^0.14.0", "typescript": "^5.1.6"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "engines": {"node": ">=8.x"}}