"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _AliwangwangOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/AliwangwangOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); } // GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY
const AliwangwangOutlined = (props, ref) => /*#__PURE__*/React.createElement(_AntdIcon.default, _extends({}, props, {
  ref: ref,
  icon: _AliwangwangOutlined.default
}));

/**![aliwangwang](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2OC4yIDM3Ny40Yy0xOC45LTQ1LjEtNDYuMy04NS42LTgxLjItMTIwLjZhMzc3LjI2IDM3Ny4yNiAwIDAwLTEyMC41LTgxLjJBMzc1LjY1IDM3NS42NSAwIDAwNTE5IDE0NS44Yy00MS45IDAtODIuOSA2LjctMTIxLjkgMjBDMzA2IDEyMy4zIDIwMC44IDEyMCAxNzAuNiAxMjBjLTIuMiAwLTcuNCAwLTkuNC4yLTExLjkuNC0yMi44IDYuNS0yOS4yIDE2LjQtNi41IDkuOS03LjcgMjIuNC0zLjQgMzMuNWw2NC4zIDE2MS42YTM3OC41OSAzNzguNTkgMCAwMC01Mi44IDE5My4yYzAgNTEuNCAxMCAxMDEgMjkuOCAxNDcuNiAxOC45IDQ1IDQ2LjIgODUuNiA4MS4yIDEyMC41IDM0LjcgMzQuOCA3NS40IDYyLjEgMTIwLjUgODEuMkM0MTguMyA4OTQgNDY3LjkgOTA0IDUxOSA5MDRjNTEuMyAwIDEwMC45LTEwLjEgMTQ3LjctMjkuOCA0NC45LTE4LjkgODUuNS00Ni4zIDEyMC40LTgxLjIgMzQuNy0zNC44IDYyLjEtNzUuNCA4MS4yLTEyMC42YTM3Ni41IDM3Ni41IDAgMDAyOS44LTE0Ny42Yy0uMi01MS4yLTEwLjEtMTAwLjgtMjkuOS0xNDcuNHptLTY2LjQgMjY2LjVhMzA3LjA4IDMwNy4wOCAwIDAxLTY1LjkgOThjLTI4LjQgMjguNS02MS4zIDUwLjctOTcuNyA2NS45aC0uMWMtMzggMTYtNzguMyAyNC4yLTExOS45IDI0LjJhMzA2LjUxIDMwNi41MSAwIDAxLTIxNy41LTkwLjJjLTI4LjQtMjguNS01MC42LTYxLjQtNjUuOC05Ny44di0uMWMtMTYtMzcuOC0yNC4xLTc4LjItMjQuMS0xMTkuOSAwLTU1LjQgMTQuOC0xMDkuNyA0Mi44LTE1N2wxMy4yLTIyLjEtOS41LTIzLjlMMjA2IDE5MmMxNC45LjYgMzUuOSAyLjEgNTkuNyA1LjYgNDMuOCA2LjUgODIuNSAxNy41IDExNC45IDMyLjZsMTkgOC45IDE5LjktNi44YzMxLjUtMTAuOCA2NC44LTE2LjIgOTguOS0xNi4yYTMwNi41MSAzMDYuNTEgMCAwMTIxNy41IDkwLjJjMjguNCAyOC41IDUwLjYgNjEuNCA2NS44IDk3LjhsLjEuMS4xLjFjMTYgMzcuNiAyNC4xIDc4IDI0LjIgMTE5LjgtLjEgNDEuNy04LjMgODItMjQuMyAxMTkuOHpNNjgxLjEgMzY0LjJjLTIwLjQgMC0zNy4xIDE2LjctMzcuMSAzNy4xdjU1LjFjMCAyMC40IDE2LjYgMzcuMSAzNy4xIDM3LjFzMzcuMS0xNi43IDM3LjEtMzcuMXYtNTUuMWMwLTIwLjUtMTYuNy0zNy4xLTM3LjEtMzcuMXptLTE3NS4yIDBjLTIwLjUgMC0zNy4xIDE2LjctMzcuMSAzNy4xdjU1LjFjMCAyMC40IDE2LjcgMzcuMSAzNy4xIDM3LjEgMjAuNSAwIDM3LjEtMTYuNyAzNy4xLTM3LjF2LTU1LjFjMC0yMC41LTE2LjctMzcuMS0zNy4xLTM3LjF6IiAvPjwvc3ZnPg==) */
const RefIcon = /*#__PURE__*/React.forwardRef(AliwangwangOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AliwangwangOutlined';
}
var _default = exports.default = RefIcon;