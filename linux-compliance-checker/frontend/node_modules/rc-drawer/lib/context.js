"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.RefContext = void 0;
var React = _interopRequireWildcard(require("react"));
var DrawerContext = /*#__PURE__*/React.createContext(null);
var RefContext = exports.RefContext = /*#__PURE__*/React.createContext({});
var _default = exports.default = DrawerContext;