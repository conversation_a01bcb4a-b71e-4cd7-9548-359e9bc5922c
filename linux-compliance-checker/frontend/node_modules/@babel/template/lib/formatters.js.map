{"version": 3, "names": ["_t", "require", "assertExpressionStatement", "makeStatementFormatter", "fn", "code", "str", "validate", "unwrap", "ast", "program", "body", "slice", "smart", "exports", "length", "statements", "statement", "Error", "expression", "start", "stmt"], "sources": ["../src/formatters.ts"], "sourcesContent": ["import { assertExpressionStatement } from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\nexport type Formatter<T> = {\n  code: (source: string) => string;\n  validate: (ast: t.File) => void;\n  unwrap: (ast: t.File) => T;\n};\n\nfunction makeStatementFormatter<T>(\n  fn: (statements: Array<t.Statement>) => T,\n): Formatter<T> {\n  return {\n    // We need to prepend a \";\" to force statement parsing so that\n    // ExpressionStatement strings won't be parsed as directives.\n    // Alongside that, we also prepend a comment so that when a syntax error\n    // is encountered, the user will be less likely to get confused about\n    // where the random semicolon came from.\n    code: str => `/* @babel/template */;\\n${str}`,\n    validate: () => {},\n    unwrap: (ast: t.File): T => {\n      return fn(ast.program.body.slice(1));\n    },\n  };\n}\n\nexport const smart = makeStatementFormatter(body => {\n  if (body.length > 1) {\n    return body;\n  } else {\n    return body[0];\n  }\n});\n\nexport const statements = makeStatementFormatter(body => body);\n\nexport const statement = makeStatementFormatter(body => {\n  // We do this validation when unwrapping since the replacement process\n  // could have added or removed statements.\n  if (body.length === 0) {\n    throw new Error(\"Found nothing to return.\");\n  }\n  if (body.length > 1) {\n    throw new Error(\"Found multiple statements but wanted one\");\n  }\n\n  return body[0];\n});\n\nexport const expression: Formatter<t.Expression> = {\n  code: str => `(\\n${str}\\n)`,\n  validate: ast => {\n    if (ast.program.body.length > 1) {\n      throw new Error(\"Found multiple statements but wanted one\");\n    }\n    if (expression.unwrap(ast).start === 0) {\n      throw new Error(\"Parse result included parens.\");\n    }\n  },\n  unwrap: ({ program }) => {\n    const [stmt] = program.body;\n    assertExpressionStatement(stmt);\n    return stmt.expression;\n  },\n};\n\nexport const program: Formatter<t.Program> = {\n  code: str => str,\n  validate: () => {},\n  unwrap: ast => ast.program,\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAAyD;EAAhDC;AAAyB,IAAAF,EAAA;AASlC,SAASG,sBAAsBA,CAC7BC,EAAyC,EAC3B;EACd,OAAO;IAMLC,IAAI,EAAEC,GAAG,IAAI,2BAA2BA,GAAG,EAAE;IAC7CC,QAAQ,EAAEA,CAAA,KAAM,CAAC,CAAC;IAClBC,MAAM,EAAGC,GAAW,IAAQ;MAC1B,OAAOL,EAAE,CAACK,GAAG,CAACC,OAAO,CAACC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IACtC;EACF,CAAC;AACH;AAEO,MAAMC,KAAK,GAAAC,OAAA,CAAAD,KAAA,GAAGV,sBAAsB,CAACQ,IAAI,IAAI;EAClD,IAAIA,IAAI,CAACI,MAAM,GAAG,CAAC,EAAE;IACnB,OAAOJ,IAAI;EACb,CAAC,MAAM;IACL,OAAOA,IAAI,CAAC,CAAC,CAAC;EAChB;AACF,CAAC,CAAC;AAEK,MAAMK,UAAU,GAAAF,OAAA,CAAAE,UAAA,GAAGb,sBAAsB,CAACQ,IAAI,IAAIA,IAAI,CAAC;AAEvD,MAAMM,SAAS,GAAAH,OAAA,CAAAG,SAAA,GAAGd,sBAAsB,CAACQ,IAAI,IAAI;EAGtD,IAAIA,IAAI,CAACI,MAAM,KAAK,CAAC,EAAE;IACrB,MAAM,IAAIG,KAAK,CAAC,0BAA0B,CAAC;EAC7C;EACA,IAAIP,IAAI,CAACI,MAAM,GAAG,CAAC,EAAE;IACnB,MAAM,IAAIG,KAAK,CAAC,0CAA0C,CAAC;EAC7D;EAEA,OAAOP,IAAI,CAAC,CAAC,CAAC;AAChB,CAAC,CAAC;AAEK,MAAMQ,UAAmC,GAAAL,OAAA,CAAAK,UAAA,GAAG;EACjDd,IAAI,EAAEC,GAAG,IAAI,MAAMA,GAAG,KAAK;EAC3BC,QAAQ,EAAEE,GAAG,IAAI;IACf,IAAIA,GAAG,CAACC,OAAO,CAACC,IAAI,CAACI,MAAM,GAAG,CAAC,EAAE;MAC/B,MAAM,IAAIG,KAAK,CAAC,0CAA0C,CAAC;IAC7D;IACA,IAAIC,UAAU,CAACX,MAAM,CAACC,GAAG,CAAC,CAACW,KAAK,KAAK,CAAC,EAAE;MACtC,MAAM,IAAIF,KAAK,CAAC,+BAA+B,CAAC;IAClD;EACF,CAAC;EACDV,MAAM,EAAEA,CAAC;IAAEE;EAAQ,CAAC,KAAK;IACvB,MAAM,CAACW,IAAI,CAAC,GAAGX,OAAO,CAACC,IAAI;IAC3BT,yBAAyB,CAACmB,IAAI,CAAC;IAC/B,OAAOA,IAAI,CAACF,UAAU;EACxB;AACF,CAAC;AAEM,MAAMT,OAA6B,GAAAI,OAAA,CAAAJ,OAAA,GAAG;EAC3CL,IAAI,EAAEC,GAAG,IAAIA,GAAG;EAChBC,QAAQ,EAAEA,CAAA,KAAM,CAAC,CAAC;EAClBC,MAAM,EAAEC,GAAG,IAAIA,GAAG,CAACC;AACrB,CAAC", "ignoreList": []}