.rc-image {
  display: inline-flex;
  position: relative;
}
.rc-image-img {
  width: 100%;
  height: auto;
}
.rc-image-img-placeholder {
  background-color: #f3f3f3;
  background-repeat: no-repeat;
  background-position: center center;
  background-image: url(data:image/svg+xml;base64,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);
}
.rc-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.rc-image-mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  transition: opacity 0.3s;
}
.rc-image:hover .rc-image-mask {
  opacity: 1;
}
.rc-image-preview {
  text-align: center;
  height: 100%;
  pointer-events: none;
}
.rc-image-preview-body {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}
.rc-image-preview.zoom-enter,
.rc-image-preview.zoom-appear {
  transform: none;
  opacity: 0;
  animation-duration: 0.3s;
}
.rc-image-preview-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.45);
}
.rc-image-preview-mask-hidden {
  display: none;
}
.rc-image-preview-img {
  cursor: grab;
  transform: scale3d(1, 1, 1);
  transition: transform 0.3s cubic-bezier(0, 0, 0.25, 1) 0s;
  user-select: none;
  vertical-align: middle;
  max-width: 100%;
  max-height: 70%;
}
.rc-image-preview-img-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.rc-image-preview-img-wrapper > * {
  pointer-events: auto;
}
.rc-image-preview-moving .rc-image-preview-img {
  cursor: grabbing;
}
.rc-image-preview-moving .rc-image-preview-img-wrapper {
  transition-duration: 0s;
}
.rc-image-preview-wrap {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  overflow: auto;
  outline: 0;
  -webkit-overflow-scrolling: touch;
}
.rc-image-preview-close {
  position: fixed;
  top: 32px;
  right: 32px;
  display: flex;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  padding: 15px;
  outline: 0;
  border: 0;
  cursor: pointer;
}
.rc-image-preview-close:hover {
  opacity: 0.3;
}
.rc-image-preview-operations-wrapper {
  position: fixed;
  z-index: 1001;
}
.rc-image-preview-footer {
  position: fixed;
  z-index: 1001;
  bottom: 32px;
  left: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.rc-image-preview-progress {
  margin-bottom: 20px;
}
.rc-image-preview-operations {
  display: flex;
  color: #bbb;
  background: rgba(0, 0, 0, 0.45);
  border-radius: 100px;
  padding: 0 20px;
}
.rc-image-preview-operations-operation {
  padding: 10px;
  cursor: pointer;
  margin-left: 10px;
  font-size: 18px;
}
.rc-image-preview-operations-operation-disabled {
  pointer-events: none;
  color: #6e6e6e;
}
.rc-image-preview-operations-operation:first-of-type {
  margin-left: 0;
}
.rc-image-preview-switch-left {
  position: fixed;
  left: 10px;
  top: 50%;
  width: 44px;
  height: 44px;
  margin-top: -22px;
  background: rgba(187, 187, 187, 0.45);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  cursor: pointer;
  color: #bbb;
}
.rc-image-preview-switch-left-disabled {
  background: rgba(187, 187, 187, 0.3);
  color: #6e6e6e;
  cursor: not-allowed;
}
.rc-image-preview-switch-left-disabled > .anticon {
  cursor: not-allowed;
}
.rc-image-preview-switch-left > .anticon {
  font-size: 24px;
}
.rc-image-preview-switch-right {
  position: fixed;
  right: 10px;
  top: 50%;
  width: 44px;
  height: 44px;
  margin-top: -22px;
  background: rgba(187, 187, 187, 0.45);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  cursor: pointer;
  color: #bbb;
}
.rc-image-preview-switch-right-disabled {
  background: rgba(187, 187, 187, 0.2);
  color: #6e6e6e;
  cursor: not-allowed;
}
.rc-image-preview-switch-right-disabled > .anticon {
  cursor: not-allowed;
}
.rc-image-preview-switch-right > .anticon {
  font-size: 24px;
}
.fade-enter,
.fade-appear {
  animation-duration: 0.3s;
  animation-fill-mode: both;
  animation-play-state: paused;
}
.fade-leave {
  animation-duration: 0.3s;
  animation-fill-mode: both;
  animation-play-state: paused;
}
.fade-enter.fade-enter-active,
.fade-appear.fade-appear-active {
  animation-name: rcImageFadeIn;
  animation-play-state: running;
}
.fade-leave.fade-leave-active {
  animation-name: rcImageFadeOut;
  animation-play-state: running;
  pointer-events: none;
}
.fade-enter,
.fade-appear {
  opacity: 0;
  animation-timing-function: linear;
}
.fade-leave {
  animation-timing-function: linear;
}
@keyframes rcImageFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes rcImageFadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.zoom-enter,
.zoom-appear {
  animation-duration: 0.3s;
  animation-fill-mode: both;
  animation-play-state: paused;
}
.zoom-leave {
  animation-duration: 0.3s;
  animation-fill-mode: both;
  animation-play-state: paused;
}
.zoom-enter.zoom-enter-active,
.zoom-appear.zoom-appear-active {
  animation-name: rcImageZoomIn;
  animation-play-state: running;
}
.zoom-leave.zoom-leave-active {
  animation-name: rcImageZoomOut;
  animation-play-state: running;
  pointer-events: none;
}
.zoom-enter,
.zoom-appear {
  transform: scale(0);
  opacity: 0;
  animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
}
.zoom-leave {
  animation-timing-function: cubic-bezier(0.78, 0.14, 0.15, 0.86);
}
@keyframes rcImageZoomIn {
  0% {
    transform: scale(0.2);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
@keyframes rcImageZoomOut {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.2);
    opacity: 0;
  }
}
