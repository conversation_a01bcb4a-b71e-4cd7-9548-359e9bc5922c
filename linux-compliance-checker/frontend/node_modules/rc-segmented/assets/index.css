.rc-segmented {
  display: inline-block;
  padding: 2px;
  background-color: rgba(0, 0, 0, 0.04);
}
.rc-segmented-group {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  border-radius: 2px;
}
.rc-segmented-item {
  position: relative;
  min-height: 28px;
  padding: 4px 10px;
  color: rgba(0, 0, 0, 0.85);
  text-align: center;
  cursor: pointer;
}
.rc-segmented-item-selected {
  background-color: white;
  color: #262626;
}
.rc-segmented-item:hover,
.rc-segmented-item:focus {
  color: #262626;
}
.rc-segmented-item-disabled,
.rc-segmented-item-disabled:hover,
.rc-segmented-item-disabled:focus {
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
}
.rc-segmented-item-label {
  z-index: 2;
  line-height: 24px;
}
.rc-segmented-item-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  opacity: 0;
  pointer-events: none;
}
.rc-segmented-thumb {
  background-color: white;
  position: absolute;
  width: 0;
  height: 100%;
  padding: 4px 0;
  transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.rc-segmented-vertical .rc-segmented-group {
  flex-direction: column;
}
.rc-segmented-vertical .rc-segmented-item {
  width: 100%;
  text-align: left;
}
.rc-segmented-vertical .rc-segmented-thumb {
  width: 100%;
  height: 0;
  padding: 0 4px;
  transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), height 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.rc-segmented-disabled .rc-segmented-item,
.rc-segmented-disabled .rc-segmented-item:hover,
.rc-segmented-disabled .rc-segmented-item:focus,
.rc-segmented-disabled .rc-segmented-item:hover,
.rc-segmented-disabled .rc-segmented-item:hover:hover,
.rc-segmented-disabled .rc-segmented-item:focus:hover,
.rc-segmented-disabled .rc-segmented-item:focus,
.rc-segmented-disabled .rc-segmented-item:hover:focus,
.rc-segmented-disabled .rc-segmented-item:focus:focus {
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
}
.rc-segmented-thumb-motion-appear-active,
.rc-segmented-thumb-motion-enter-active {
  transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  will-change: transform, width;
}
.rc-segmented-rtl {
  direction: rtl;
}
.rc-segmented-item:focus {
  outline: none;
}
.rc-segmented-item-focused {
  border-radius: 2px;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
