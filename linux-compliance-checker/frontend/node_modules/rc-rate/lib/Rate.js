"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _objectWithoutProperties2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));
var _classnames = _interopRequireDefault(require("classnames"));
var _useMergedState5 = _interopRequireDefault(require("rc-util/lib/hooks/useMergedState"));
var _KeyCode = _interopRequireDefault(require("rc-util/lib/KeyCode"));
var _pickAttrs = _interopRequireDefault(require("rc-util/lib/pickAttrs"));
var _react = _interopRequireDefault(require("react"));
var _Star = _interopRequireDefault(require("./Star"));
var _useRefs3 = _interopRequireDefault(require("./useRefs"));
var _util = require("./util");
var _excluded = ["prefixCls", "className", "defaultValue", "value", "count", "allowHalf", "allowClear", "keyboard", "character", "characterRender", "disabled", "direction", "tabIndex", "autoFocus", "onHoverChange", "onChange", "onFocus", "onBlur", "onKeyDown", "onMouseLeave"];
function Rate(props, ref) {
  var _props$prefixCls = props.prefixCls,
    prefixCls = _props$prefixCls === void 0 ? 'rc-rate' : _props$prefixCls,
    className = props.className,
    defaultValue = props.defaultValue,
    propValue = props.value,
    _props$count = props.count,
    count = _props$count === void 0 ? 5 : _props$count,
    _props$allowHalf = props.allowHalf,
    allowHalf = _props$allowHalf === void 0 ? false : _props$allowHalf,
    _props$allowClear = props.allowClear,
    allowClear = _props$allowClear === void 0 ? true : _props$allowClear,
    _props$keyboard = props.keyboard,
    keyboard = _props$keyboard === void 0 ? true : _props$keyboard,
    _props$character = props.character,
    character = _props$character === void 0 ? '★' : _props$character,
    characterRender = props.characterRender,
    disabled = props.disabled,
    _props$direction = props.direction,
    direction = _props$direction === void 0 ? 'ltr' : _props$direction,
    _props$tabIndex = props.tabIndex,
    tabIndex = _props$tabIndex === void 0 ? 0 : _props$tabIndex,
    autoFocus = props.autoFocus,
    onHoverChange = props.onHoverChange,
    onChange = props.onChange,
    onFocus = props.onFocus,
    onBlur = props.onBlur,
    onKeyDown = props.onKeyDown,
    onMouseLeave = props.onMouseLeave,
    restProps = (0, _objectWithoutProperties2.default)(props, _excluded);
  var _useRefs = (0, _useRefs3.default)(),
    _useRefs2 = (0, _slicedToArray2.default)(_useRefs, 2),
    getStarRef = _useRefs2[0],
    setStarRef = _useRefs2[1];
  var rateRef = _react.default.useRef(null);

  // ============================ Ref =============================
  var triggerFocus = function triggerFocus() {
    if (!disabled) {
      var _rateRef$current;
      (_rateRef$current = rateRef.current) === null || _rateRef$current === void 0 || _rateRef$current.focus();
    }
  };
  _react.default.useImperativeHandle(ref, function () {
    return {
      focus: triggerFocus,
      blur: function blur() {
        if (!disabled) {
          var _rateRef$current2;
          (_rateRef$current2 = rateRef.current) === null || _rateRef$current2 === void 0 || _rateRef$current2.blur();
        }
      }
    };
  });

  // =========================== Value ============================
  var _useMergedState = (0, _useMergedState5.default)(defaultValue || 0, {
      value: propValue
    }),
    _useMergedState2 = (0, _slicedToArray2.default)(_useMergedState, 2),
    value = _useMergedState2[0],
    setValue = _useMergedState2[1];
  var _useMergedState3 = (0, _useMergedState5.default)(null),
    _useMergedState4 = (0, _slicedToArray2.default)(_useMergedState3, 2),
    cleanedValue = _useMergedState4[0],
    setCleanedValue = _useMergedState4[1];
  var getStarValue = function getStarValue(index, x) {
    var reverse = direction === 'rtl';
    var starValue = index + 1;
    if (allowHalf) {
      var starEle = getStarRef(index);
      var leftDis = (0, _util.getOffsetLeft)(starEle);
      var width = starEle.clientWidth;
      if (reverse && x - leftDis > width / 2) {
        starValue -= 0.5;
      } else if (!reverse && x - leftDis < width / 2) {
        starValue -= 0.5;
      }
    }
    return starValue;
  };

  // >>>>> Change
  var changeValue = function changeValue(nextValue) {
    setValue(nextValue);
    onChange === null || onChange === void 0 || onChange(nextValue);
  };

  // =========================== Focus ============================
  var _React$useState = _react.default.useState(false),
    _React$useState2 = (0, _slicedToArray2.default)(_React$useState, 2),
    focused = _React$useState2[0],
    setFocused = _React$useState2[1];
  var onInternalFocus = function onInternalFocus() {
    setFocused(true);
    onFocus === null || onFocus === void 0 || onFocus();
  };
  var onInternalBlur = function onInternalBlur() {
    setFocused(false);
    onBlur === null || onBlur === void 0 || onBlur();
  };

  // =========================== Hover ============================
  var _React$useState3 = _react.default.useState(null),
    _React$useState4 = (0, _slicedToArray2.default)(_React$useState3, 2),
    hoverValue = _React$useState4[0],
    setHoverValue = _React$useState4[1];
  var onHover = function onHover(event, index) {
    var nextHoverValue = getStarValue(index, event.pageX);
    if (nextHoverValue !== cleanedValue) {
      setHoverValue(nextHoverValue);
      setCleanedValue(null);
    }
    onHoverChange === null || onHoverChange === void 0 || onHoverChange(nextHoverValue);
  };
  var onMouseLeaveCallback = function onMouseLeaveCallback(event) {
    if (!disabled) {
      setHoverValue(null);
      setCleanedValue(null);
      onHoverChange === null || onHoverChange === void 0 || onHoverChange(undefined);
    }
    if (event) {
      onMouseLeave === null || onMouseLeave === void 0 || onMouseLeave(event);
    }
  };

  // =========================== Click ============================
  var onClick = function onClick(event, index) {
    var newValue = getStarValue(index, event.pageX);
    var isReset = false;
    if (allowClear) {
      isReset = newValue === value;
    }
    onMouseLeaveCallback();
    changeValue(isReset ? 0 : newValue);
    setCleanedValue(isReset ? newValue : null);
  };
  var onInternalKeyDown = function onInternalKeyDown(event) {
    var keyCode = event.keyCode;
    var reverse = direction === 'rtl';
    var step = allowHalf ? 0.5 : 1;
    if (keyboard) {
      if (keyCode === _KeyCode.default.RIGHT && value < count && !reverse) {
        changeValue(value + step);
        event.preventDefault();
      } else if (keyCode === _KeyCode.default.LEFT && value > 0 && !reverse) {
        changeValue(value - step);
        event.preventDefault();
      } else if (keyCode === _KeyCode.default.RIGHT && value > 0 && reverse) {
        changeValue(value - step);
        event.preventDefault();
      } else if (keyCode === _KeyCode.default.LEFT && value < count && reverse) {
        changeValue(value + step);
        event.preventDefault();
      }
    }
    onKeyDown === null || onKeyDown === void 0 || onKeyDown(event);
  };

  // =========================== Effect ===========================

  _react.default.useEffect(function () {
    if (autoFocus && !disabled) {
      triggerFocus();
    }
  }, []);

  // =========================== Render ===========================
  // >>> Star
  var starNodes = new Array(count).fill(0).map(function (item, index) {
    return /*#__PURE__*/_react.default.createElement(_Star.default, {
      ref: setStarRef(index),
      index: index,
      count: count,
      disabled: disabled,
      prefixCls: "".concat(prefixCls, "-star"),
      allowHalf: allowHalf,
      value: hoverValue === null ? value : hoverValue,
      onClick: onClick,
      onHover: onHover,
      key: item || index,
      character: character,
      characterRender: characterRender,
      focused: focused
    });
  });
  var classString = (0, _classnames.default)(prefixCls, className, (0, _defineProperty2.default)((0, _defineProperty2.default)({}, "".concat(prefixCls, "-disabled"), disabled), "".concat(prefixCls, "-rtl"), direction === 'rtl'));

  // >>> Node
  return /*#__PURE__*/_react.default.createElement("ul", (0, _extends2.default)({
    className: classString,
    onMouseLeave: onMouseLeaveCallback,
    tabIndex: disabled ? -1 : tabIndex,
    onFocus: disabled ? null : onInternalFocus,
    onBlur: disabled ? null : onInternalBlur,
    onKeyDown: disabled ? null : onInternalKeyDown,
    ref: rateRef
  }, (0, _pickAttrs.default)(restProps, {
    aria: true,
    data: true,
    attr: true
  })), starNodes);
}
var _default = exports.default = /*#__PURE__*/_react.default.forwardRef(Rate);