{"name": "rc-motion", "version": "2.9.5", "description": "React lifecycle controlled motion library", "keywords": ["react", "react-component", "react-motion", "motion", "antd", "ant-design"], "homepage": "https://react-component.github.io/motion", "bugs": {"url": "http://github.com/react-component/motion/issues"}, "repository": {"type": "git", "url": "**************:react-component/motion.git"}, "license": "MIT", "main": "./lib/index", "module": "./es/index", "typings": "es/index.d.ts", "files": ["es", "lib"], "scripts": {"compile": "father build", "docs:build": "dumi build", "docs:deploy": "gh-pages -d .doc", "lint": "eslint src/ --ext .tsx,.ts", "now-build": "npm run docs:build", "prepare": "husky install", "prepublishOnly": "npm run compile && np --yolo --no-publish", "prettier": "prettier --write \"**/*.{js,jsx,tsx,ts,less,md,json}\"", "postpublish": "npm run docs:build && npm run docs:deploy", "start": "dumi dev", "test": "rc-test"}, "lint-staged": {"**/*.{js,jsx,tsx,ts,md,json}": ["prettier --write", "git add"]}, "dependencies": {"@babel/runtime": "^7.11.1", "classnames": "^2.2.1", "rc-util": "^5.44.0"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^15.0.7", "@types/classnames": "^2.2.9", "@types/jest": "^26.0.8", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@umijs/fabric": "^2.0.8", "cross-env": "^7.0.2", "dumi": "^2.0.18", "eslint": "^7.0.0", "father": "^4.1.2", "gh-pages": "^6.0.0", "husky": "^8.0.3", "lint-staged": "^14.0.1", "np": "^6.2.4", "prettier": "^2.1.1", "rc-test": "^7.0.14", "react": "^18.3.0", "react-dom": "^18.3.0", "typescript": "^4.0.3"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "cnpm": {"mode": "npm"}, "tnpm": {"mode": "npm"}}