import type { CSSProperties, InputHT<PERSON><PERSON>ttributes, KeyboardEventHandler, MouseEventHandler, ReactElement, ReactNode } from 'react';
import type { InputFocusOptions } from './utils/commonUtils';
import type { LiteralUnion } from './utils/types';
export interface CommonInputProps {
    prefix?: ReactNode;
    suffix?: ReactNode;
    addonBefore?: ReactNode;
    addonAfter?: ReactNode;
    /** @deprecated Use `classNames` instead */
    classes?: {
        affixWrapper?: string;
        group?: string;
        wrapper?: string;
    };
    classNames?: {
        affixWrapper?: string;
        prefix?: string;
        suffix?: string;
        groupWrapper?: string;
        wrapper?: string;
        variant?: string;
    };
    styles?: {
        affixWrapper?: CSSProperties;
        prefix?: CSSProperties;
        suffix?: CSSProperties;
    };
    allowClear?: boolean | {
        clearIcon?: ReactNode;
    };
}
type DataAttr = Record<`data-${string}`, string>;
export type ValueType = InputHTMLAttributes<HTMLInputElement>['value'] | bigint;
export interface BaseInputProps extends CommonInputProps {
    value?: ValueType;
    /** @deprecated Use `children` instead */
    inputElement?: ReactElement;
    prefixCls?: string;
    className?: string;
    style?: CSSProperties;
    disabled?: boolean;
    focused?: boolean;
    triggerFocus?: () => void;
    readOnly?: boolean;
    handleReset?: MouseEventHandler;
    onClear?: () => void;
    hidden?: boolean;
    dataAttrs?: {
        affixWrapper?: DataAttr;
    };
    components?: {
        affixWrapper?: 'span' | 'div';
        groupWrapper?: 'span' | 'div';
        wrapper?: 'span' | 'div';
        groupAddon?: 'span' | 'div';
    };
    children: ReactElement;
}
export type ShowCountFormatter = (args: {
    value: string;
    count: number;
    maxLength?: number;
}) => ReactNode;
export type ExceedFormatter = (value: string, config: {
    max: number;
}) => string;
export interface CountConfig {
    max?: number;
    strategy?: (value: string) => number;
    show?: boolean | ShowCountFormatter;
    /** Trigger when content larger than the `max` limitation */
    exceedFormatter?: ExceedFormatter;
}
export interface InputProps extends CommonInputProps, Omit<InputHTMLAttributes<HTMLInputElement>, 'size' | 'prefix' | 'type' | 'value'> {
    value?: ValueType;
    prefixCls?: string;
    type?: LiteralUnion<'button' | 'checkbox' | 'color' | 'date' | 'datetime-local' | 'email' | 'file' | 'hidden' | 'image' | 'month' | 'number' | 'password' | 'radio' | 'range' | 'reset' | 'search' | 'submit' | 'tel' | 'text' | 'time' | 'url' | 'week', string>;
    onPressEnter?: KeyboardEventHandler<HTMLInputElement>;
    /** It's better to use `count.show` instead */
    showCount?: boolean | {
        formatter: ShowCountFormatter;
    };
    autoComplete?: string;
    htmlSize?: number;
    classNames?: CommonInputProps['classNames'] & {
        input?: string;
        count?: string;
    };
    styles?: CommonInputProps['styles'] & {
        input?: CSSProperties;
        count?: CSSProperties;
    };
    count?: CountConfig;
    onClear?: () => void;
}
export interface InputRef {
    focus: (options?: InputFocusOptions) => void;
    blur: () => void;
    setSelectionRange: (start: number, end: number, direction?: 'forward' | 'backward' | 'none') => void;
    select: () => void;
    input: HTMLInputElement | null;
    nativeElement: HTMLElement | null;
}
export interface ChangeEventInfo {
    source: 'compositionEnd' | 'change';
}
export {};
