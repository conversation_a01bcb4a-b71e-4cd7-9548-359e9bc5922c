"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _rcMenu = _interopRequireWildcard(require("rc-menu"));
var React = _interopRequireWildcard(require("react"));
var _MentionsContext = _interopRequireDefault(require("./MentionsContext"));
/**
 * We only use Menu to display the candidate.
 * The focus is controlled by textarea to make accessibility easy.
 */
function DropdownMenu(props) {
  var _React$useContext = React.useContext(_MentionsContext.default),
    notFoundContent = _React$useContext.notFoundContent,
    activeIndex = _React$useContext.activeIndex,
    setActiveIndex = _React$useContext.setActiveIndex,
    selectOption = _React$useContext.selectOption,
    onFocus = _React$useContext.onFocus,
    onBlur = _React$useContext.onBlur,
    onScroll = _React$useContext.onScroll;
  var prefixCls = props.prefixCls,
    options = props.options;
  var activeOption = options[activeIndex] || {};
  return /*#__PURE__*/React.createElement(_rcMenu.default, {
    prefixCls: "".concat(prefixCls, "-menu"),
    activeKey: activeOption.key,
    onSelect: function onSelect(_ref) {
      var key = _ref.key;
      var option = options.find(function (_ref2) {
        var optionKey = _ref2.key;
        return optionKey === key;
      });
      selectOption(option);
    },
    onFocus: onFocus,
    onBlur: onBlur,
    onScroll: onScroll
  }, options.map(function (option, index) {
    var key = option.key,
      disabled = option.disabled,
      className = option.className,
      style = option.style,
      label = option.label;
    return /*#__PURE__*/React.createElement(_rcMenu.MenuItem, {
      key: key,
      disabled: disabled,
      className: className,
      style: style,
      onMouseEnter: function onMouseEnter() {
        setActiveIndex(index);
      }
    }, label);
  }), !options.length && /*#__PURE__*/React.createElement(_rcMenu.MenuItem, {
    disabled: true
  }, notFoundContent));
}
var _default = exports.default = DropdownMenu;