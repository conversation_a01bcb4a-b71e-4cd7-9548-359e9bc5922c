{"name": "@rc-component/context", "version": "1.4.0", "description": "React way perf context selector", "keywords": ["react", "react-component", "context"], "homepage": "http://github.com/react-component/context", "bugs": {"url": "http://github.com/react-component/context/issues"}, "repository": {"type": "git", "url": "**************:react-component/context.git"}, "license": "MIT", "author": "", "main": "./lib/index", "module": "./es/index", "files": ["lib", "es"], "scripts": {"build": "dumi build", "compile": "father build", "coverage": "father test --coverage", "lint": "eslint src/ docs/ --ext .tsx,.ts,.jsx,.js", "now-build": "npm run build", "prepublishOnly": "npm run compile && np --yolo --no-publish", "start": "dumi dev", "test": "rc-test", "tsc": "tsc --noEmit"}, "dependencies": {"@babel/runtime": "^7.10.1", "rc-util": "^5.27.0"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.0", "@types/jest": "^24.0.18", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/warning": "^3.0.0", "@umijs/fabric": "^3.0.0", "cross-env": "^7.0.3", "dumi": "^2.0.15", "eslint": "^7.1.0", "father": "^4.0.0", "np": "^5.0.3", "rc-test": "^7.0.14", "react": "^18.0.0", "react-dom": "^18.0.0", "typescript": "^4.0.3"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}