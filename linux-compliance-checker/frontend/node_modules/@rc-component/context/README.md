# @rc-component/context

---

React way perf context selector

[![NPM version][npm-image]][npm-url] [![build status][github-actions-image]][github-actions-url] [![Codecov][codecov-image]][codecov-url] [![node version][node-image]][node-url] [![npm download][download-image]][download-url]

[npm-image]: http://img.shields.io/npm/v/@rc-component/context.svg?style=flat-square
[npm-url]: http://npmjs.org/package/@rc-component/context
[github-actions-image]: https://github.com/react-component/context/workflows/CI/badge.svg
[github-actions-url]: https://github.com/react-component/context/actions
[codecov-image]: https://img.shields.io/codecov/c/github/react-component/context/master.svg?style=flat-square
[codecov-url]: https://app.codecov.io/gh/react-component/context
[node-image]: https://img.shields.io/badge/node.js-%3E=_0.10-green.svg?style=flat-square
[node-url]: http://nodejs.org/download/
[download-image]: https://img.shields.io/npm/dm/@rc-component/context.svg?style=flat-square
[download-url]: https://npmjs.org/package/@rc-component/context

## Development

```
npm install
npm start
```
