"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _common = require("./common");
var locale = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, _common.commonLocale), {}, {
  locale: 'bg_BG',
  today: 'Днес',
  now: 'Сега',
  backToToday: 'Към днес',
  ok: 'Добре',
  clear: 'Изчистване',
  week: 'Седмица',
  month: 'Месец',
  year: 'Година',
  timeSelect: 'Избор на час',
  dateSelect: 'Избор на дата',
  monthSelect: 'Избор на месец',
  yearSelect: 'Избор на година',
  decadeSelect: 'Десетилетие',
  dateFormat: 'D M YYYY',
  dateTimeFormat: 'D M YYYY HH:mm:ss',
  previousMonth: 'Предишен месец (PageUp)',
  nextMonth: 'Следващ месец (PageDown)',
  previousYear: 'Последна година (Control + left)',
  nextYear: 'Следваща година (Control + right)',
  previousDecade: 'Предишно десетилетие',
  nextDecade: 'Следващо десетилетие',
  previousCentury: 'Последен век',
  nextCentury: 'Следващ век'
});
var _default = exports.default = locale;