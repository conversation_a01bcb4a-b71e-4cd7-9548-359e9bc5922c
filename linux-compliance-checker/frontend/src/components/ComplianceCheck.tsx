import React, { useState } from 'react';
import { 
  Card, 
  Button, 
  Select, 
  Table, 
  Progress, 
  Tag, 
  Space, 
  Row, 
  Col,
  Checkbox,
  message,
  Modal,
  Steps,
  Alert,
  Spin
} from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  StopOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { Step } = Steps;

interface CheckItem {
  id: string;
  category: string;
  name: string;
  description: string;
  level: 'high' | 'medium' | 'low';
  status: 'pending' | 'running' | 'passed' | 'failed' | 'warning';
  progress: number;
}

interface CheckTarget {
  id: string;
  name: string;
  type: 'host' | 'group';
  selected: boolean;
}

const ComplianceCheck: React.FC = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [checkTargets, setCheckTargets] = useState<CheckTarget[]>([
    { id: '1', name: 'web-server-01', type: 'host', selected: false },
    { id: '2', name: 'db-server-01', type: 'host', selected: false },
    { id: '3', name: 'Web服务器组', type: 'group', selected: false },
    { id: '4', name: '数据库服务器组', type: 'group', selected: false },
  ]);
  
  const [checkItems, setCheckItems] = useState<CheckItem[]>([
    {
      id: '1',
      category: '身份鉴别',
      name: '用户身份标识唯一性',
      description: '检查系统用户身份标识的唯一性',
      level: 'high',
      status: 'pending',
      progress: 0
    },
    {
      id: '2',
      category: '身份鉴别',
      name: '用户身份鉴别信息复杂度',
      description: '检查用户密码复杂度策略',
      level: 'high',
      status: 'pending',
      progress: 0
    },
    {
      id: '3',
      category: '访问控制',
      name: '访问控制策略',
      description: '检查系统访问控制策略配置',
      level: 'high',
      status: 'pending',
      progress: 0
    },
    {
      id: '4',
      category: '安全审计',
      name: '审计记录内容',
      description: '检查系统审计记录的完整性',
      level: 'medium',
      status: 'pending',
      progress: 0
    },
    {
      id: '5',
      category: '通信完整性',
      name: '通信过程完整性',
      description: '检查网络通信完整性保护',
      level: 'medium',
      status: 'pending',
      progress: 0
    }
  ]);

  const [isRunning, setIsRunning] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [isModalVisible, setIsModalVisible] = useState(false);

  const getLevelTag = (level: string) => {
    switch (level) {
      case 'high':
        return <Tag color="red">高</Tag>;
      case 'medium':
        return <Tag color="orange">中</Tag>;
      case 'low':
        return <Tag color="green">低</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'passed':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'failed':
        return <CloseCircleOutlined style={{ color: '#f5222d' }} />;
      case 'warning':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'running':
        return <Spin size="small" />;
      default:
        return <InfoCircleOutlined style={{ color: '#d9d9d9' }} />;
    }
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'passed':
        return <Tag color="success">通过</Tag>;
      case 'failed':
        return <Tag color="error">失败</Tag>;
      case 'warning':
        return <Tag color="warning">警告</Tag>;
      case 'running':
        return <Tag color="processing">检查中</Tag>;
      default:
        return <Tag>待检查</Tag>;
    }
  };

  const columns = [
    {
      title: '检查项',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: CheckItem) => (
        <Space>
          {getStatusIcon(record.status)}
          {text}
        </Space>
      ),
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '重要性',
      dataIndex: 'level',
      key: 'level',
      render: (level: string) => getLevelTag(level),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number, record: CheckItem) => (
        <Progress 
          percent={progress} 
          size="small" 
          status={record.status === 'failed' ? 'exception' : 'active'}
        />
      ),
    },
  ];

  const handleTargetChange = (targetId: string, checked: boolean) => {
    setCheckTargets(targets =>
      targets.map(target =>
        target.id === targetId ? { ...target, selected: checked } : target
      )
    );
  };

  const handleStartCheck = () => {
    const selectedTargets = checkTargets.filter(target => target.selected);
    if (selectedTargets.length === 0) {
      message.warning('请选择检查目标');
      return;
    }
    if (!selectedTemplate) {
      message.warning('请选择检查模板');
      return;
    }

    setIsModalVisible(true);
    setIsRunning(true);
    setCurrentStep(0);

    // 模拟检查过程
    simulateCheck();
  };

  const simulateCheck = () => {
    let step = 0;
    const totalSteps = 4;
    
    const stepInterval = setInterval(() => {
      setCurrentStep(step);
      
      if (step < totalSteps) {
        step++;
      } else {
        clearInterval(stepInterval);
        
        // 模拟检查项执行
        let itemIndex = 0;
        const itemInterval = setInterval(() => {
          if (itemIndex < checkItems.length) {
            setCheckItems(items =>
              items.map((item, index) => {
                if (index === itemIndex) {
                  return {
                    ...item,
                    status: 'running',
                    progress: 0
                  };
                }
                return item;
              })
            );

            // 模拟进度更新
            let progress = 0;
            const progressInterval = setInterval(() => {
              progress += 20;
              setCheckItems(items =>
                items.map((item, index) => {
                  if (index === itemIndex) {
                    if (progress >= 100) {
                      const statuses = ['passed', 'failed', 'warning'];
                      const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
                      return {
                        ...item,
                        status: randomStatus as any,
                        progress: 100
                      };
                    }
                    return {
                      ...item,
                      progress
                    };
                  }
                  return item;
                })
              );

              if (progress >= 100) {
                clearInterval(progressInterval);
                itemIndex++;
              }
            }, 300);
          } else {
            clearInterval(itemInterval);
            setIsRunning(false);
            message.success('合规检查完成');
          }
        }, 1500);
      }
    }, 1000);
  };

  const handleStopCheck = () => {
    setIsRunning(false);
    setCheckItems(items =>
      items.map(item => ({
        ...item,
        status: 'pending',
        progress: 0
      }))
    );
    message.info('检查已停止');
  };

  const steps = [
    {
      title: '准备检查',
      description: '初始化检查环境'
    },
    {
      title: '连接目标',
      description: '连接到目标主机'
    },
    {
      title: '收集信息',
      description: '收集系统配置信息'
    },
    {
      title: '执行检查',
      description: '执行合规检查项'
    },
    {
      title: '生成报告',
      description: '生成检查报告'
    }
  ];

  return (
    <div>
      <Row gutter={16}>
        <Col span={8}>
          <Card title="检查配置" style={{ marginBottom: 16 }}>
            <div style={{ marginBottom: 16 }}>
              <label style={{ display: 'block', marginBottom: 8 }}>检查模板：</label>
              <Select
                style={{ width: '100%' }}
                placeholder="选择检查模板"
                value={selectedTemplate}
                onChange={setSelectedTemplate}
              >
                <Option value="level3-web">等保三级Web服务器模板</Option>
                <Option value="level3-db">等保三级数据库服务器模板</Option>
                <Option value="level3-app">等保三级应用服务器模板</Option>
                <Option value="level3-file">等保三级文件服务器模板</Option>
              </Select>
            </div>

            <div style={{ marginBottom: 16 }}>
              <label style={{ display: 'block', marginBottom: 8 }}>检查目标：</label>
              {checkTargets.map(target => (
                <div key={target.id} style={{ marginBottom: 8 }}>
                  <Checkbox
                    checked={target.selected}
                    onChange={(e) => handleTargetChange(target.id, e.target.checked)}
                  >
                    {target.type === 'group' ? '📁' : '💻'} {target.name}
                  </Checkbox>
                </div>
              ))}
            </div>

            <Space style={{ width: '100%' }}>
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={handleStartCheck}
                disabled={isRunning}
                style={{ flex: 1 }}
              >
                开始检查
              </Button>
              <Button
                danger
                icon={<StopOutlined />}
                onClick={handleStopCheck}
                disabled={!isRunning}
              >
                停止
              </Button>
            </Space>
          </Card>

          {isRunning && (
            <Card title="检查进度">
              <Steps direction="vertical" size="small" current={currentStep}>
                {steps.map((step, index) => (
                  <Step
                    key={index}
                    title={step.title}
                    description={step.description}
                  />
                ))}
              </Steps>
            </Card>
          )}
        </Col>

        <Col span={16}>
          <Card title="检查项目">
            {isRunning && (
              <Alert
                message="正在执行合规检查"
                description="请耐心等待检查完成，不要关闭页面"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}
            
            <Table
              columns={columns}
              dataSource={checkItems}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>

      <Modal
        title="检查执行中"
        open={isModalVisible}
        footer={null}
        closable={false}
        width={600}
      >
        <div style={{ textAlign: 'center', padding: '20px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16, fontSize: 16 }}>
            正在执行合规检查，请稍候...
          </div>
          <div style={{ marginTop: 24 }}>
            <Steps current={currentStep} size="small">
              {steps.map((step, index) => (
                <Step key={index} title={step.title} />
              ))}
            </Steps>
          </div>
          <div style={{ marginTop: 24 }}>
            <Button onClick={() => setIsModalVisible(false)}>
              后台运行
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default ComplianceCheck;
