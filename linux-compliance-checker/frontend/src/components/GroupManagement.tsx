import React, { useState } from 'react';
import { 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Tag, 
  Space, 
  Popconfirm,
  Card,
  Row,
  Col,
  message,
  Descriptions,
  List
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  EyeOutlined,
  TeamOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { TextArea } = Input;

interface Group {
  id: string;
  name: string;
  description: string;
  type: string;
  hostCount: number;
  complianceTemplate: string;
  createdAt: string;
  hosts: string[];
}

const GroupManagement: React.FC = () => {
  const [groups, setGroups] = useState<Group[]>([
    {
      id: '1',
      name: 'Web服务器',
      description: 'Web应用服务器组，包含所有对外提供Web服务的服务器',
      type: 'production',
      hostCount: 8,
      complianceTemplate: '等保三级Web服务器模板',
      createdAt: '2024-01-10 10:30:00',
      hosts: ['web-server-01', 'web-server-02', 'web-server-03']
    },
    {
      id: '2',
      name: '数据库服务器',
      description: '数据库服务器组，包含MySQL、PostgreSQL等数据库服务器',
      type: 'production',
      hostCount: 4,
      complianceTemplate: '等保三级数据库服务器模板',
      createdAt: '2024-01-10 10:35:00',
      hosts: ['db-server-01', 'db-server-02']
    },
    {
      id: '3',
      name: '应用服务器',
      description: '应用服务器组，包含业务应用服务器',
      type: 'production',
      hostCount: 6,
      complianceTemplate: '等保三级应用服务器模板',
      createdAt: '2024-01-10 10:40:00',
      hosts: ['app-server-01', 'app-server-02', 'app-server-03']
    },
    {
      id: '4',
      name: '测试环境',
      description: '测试环境服务器组',
      type: 'test',
      hostCount: 3,
      complianceTemplate: '等保三级测试环境模板',
      createdAt: '2024-01-10 10:45:00',
      hosts: ['test-server-01']
    }
  ]);

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [editingGroup, setEditingGroup] = useState<Group | null>(null);
  const [viewingGroup, setViewingGroup] = useState<Group | null>(null);
  const [form] = Form.useForm();

  const getTypeTag = (type: string) => {
    switch (type) {
      case 'production':
        return <Tag color="red">生产环境</Tag>;
      case 'test':
        return <Tag color="blue">测试环境</Tag>;
      case 'development':
        return <Tag color="green">开发环境</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  const columns = [
    {
      title: '组名',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => (
        <Space>
          <TeamOutlined />
          {text}
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '环境类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => getTypeTag(type),
    },
    {
      title: '主机数量',
      dataIndex: 'hostCount',
      key: 'hostCount',
      render: (count: number) => <Tag color="blue">{count}台</Tag>,
    },
    {
      title: '合规模板',
      dataIndex: 'complianceTemplate',
      key: 'complianceTemplate',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Group) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button 
            type="link" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个组吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingGroup(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (group: Group) => {
    setEditingGroup(group);
    form.setFieldsValue(group);
    setIsModalVisible(true);
  };

  const handleView = (group: Group) => {
    setViewingGroup(group);
    setIsDetailModalVisible(true);
  };

  const handleDelete = (id: string) => {
    setGroups(groups.filter(group => group.id !== id));
    message.success('组删除成功');
  };

  const handleOk = () => {
    form.validateFields().then(values => {
      if (editingGroup) {
        setGroups(groups.map(group => 
          group.id === editingGroup.id ? { ...group, ...values } : group
        ));
        message.success('组更新成功');
      } else {
        const newGroup: Group = {
          id: Date.now().toString(),
          ...values,
          hostCount: 0,
          createdAt: new Date().toLocaleString(),
          hosts: []
        };
        setGroups([...groups, newGroup]);
        message.success('组添加成功');
      }
      setIsModalVisible(false);
    });
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  return (
    <div>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <h2>组管理</h2>
          </Col>
          <Col>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              添加组
            </Button>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={groups}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个组`,
          }}
        />
      </Card>

      <Modal
        title={editingGroup ? '编辑组' : '添加组'}
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="确定"
        cancelText="取消"
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          name="groupForm"
        >
          <Form.Item
            name="name"
            label="组名"
            rules={[{ required: true, message: '请输入组名' }]}
          >
            <Input placeholder="请输入组名" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入组描述' }]}
          >
            <TextArea rows={3} placeholder="请输入组描述" />
          </Form.Item>
          <Form.Item
            name="type"
            label="环境类型"
            rules={[{ required: true, message: '请选择环境类型' }]}
          >
            <Select placeholder="请选择环境类型">
              <Option value="production">生产环境</Option>
              <Option value="test">测试环境</Option>
              <Option value="development">开发环境</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="complianceTemplate"
            label="合规模板"
            rules={[{ required: true, message: '请选择合规模板' }]}
          >
            <Select placeholder="请选择合规模板">
              <Option value="等保三级Web服务器模板">等保三级Web服务器模板</Option>
              <Option value="等保三级数据库服务器模板">等保三级数据库服务器模板</Option>
              <Option value="等保三级应用服务器模板">等保三级应用服务器模板</Option>
              <Option value="等保三级文件服务器模板">等保三级文件服务器模板</Option>
              <Option value="等保三级测试环境模板">等保三级测试环境模板</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="组详情"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {viewingGroup && (
          <div>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="组名">{viewingGroup.name}</Descriptions.Item>
              <Descriptions.Item label="环境类型">{getTypeTag(viewingGroup.type)}</Descriptions.Item>
              <Descriptions.Item label="主机数量" span={2}>
                <Tag color="blue">{viewingGroup.hostCount}台</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="合规模板" span={2}>
                {viewingGroup.complianceTemplate}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间" span={2}>
                {viewingGroup.createdAt}
              </Descriptions.Item>
              <Descriptions.Item label="描述" span={2}>
                {viewingGroup.description}
              </Descriptions.Item>
            </Descriptions>
            
            <div style={{ marginTop: 24 }}>
              <h4>包含的主机</h4>
              <List
                size="small"
                bordered
                dataSource={viewingGroup.hosts}
                renderItem={(host) => (
                  <List.Item>
                    <Space>
                      <TeamOutlined />
                      {host}
                    </Space>
                  </List.Item>
                )}
              />
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default GroupManagement;
