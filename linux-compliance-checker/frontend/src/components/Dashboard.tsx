import React from 'react';
import { Card, Row, Col, Statistic, Progress, List, Tag } from 'antd';
import { 
  DesktopOutlined, 
  TeamOutlined, 
  SafetyOutlined, 
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined 
} from '@ant-design/icons';

const Dashboard: React.FC = () => {
  const recentChecks = [
    {
      id: 1,
      host: '*************',
      status: 'success',
      time: '2024-01-15 14:30:25',
      score: 85
    },
    {
      id: 2,
      host: '*************',
      status: 'warning',
      time: '2024-01-15 14:25:10',
      score: 72
    },
    {
      id: 3,
      host: '*************',
      status: 'error',
      time: '2024-01-15 14:20:45',
      score: 45
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'warning':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'error':
        return <CloseCircleOutlined style={{ color: '#f5222d' }} />;
      default:
        return <CheckCircleOutlined />;
    }
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'success':
        return <Tag color="success">通过</Tag>;
      case 'warning':
        return <Tag color="warning">警告</Tag>;
      case 'error':
        return <Tag color="error">失败</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="主机总数"
              value={24}
              prefix={<DesktopOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="主机组数"
              value={6}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日检查"
              value={18}
              prefix={<SafetyOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="合规率"
              value={78.5}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Card title="系统合规状态" style={{ height: 400 }}>
            <div style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 8 }}>
                <span>身份鉴别</span>
                <Progress percent={85} status="active" strokeColor="#52c41a" />
              </div>
              <div style={{ marginBottom: 8 }}>
                <span>访问控制</span>
                <Progress percent={72} status="active" strokeColor="#faad14" />
              </div>
              <div style={{ marginBottom: 8 }}>
                <span>安全审计</span>
                <Progress percent={90} status="active" strokeColor="#52c41a" />
              </div>
              <div style={{ marginBottom: 8 }}>
                <span>通信完整性</span>
                <Progress percent={65} status="active" strokeColor="#f5222d" />
              </div>
              <div style={{ marginBottom: 8 }}>
                <span>通信保密性</span>
                <Progress percent={78} status="active" strokeColor="#faad14" />
              </div>
              <div style={{ marginBottom: 8 }}>
                <span>数据完整性</span>
                <Progress percent={88} status="active" strokeColor="#52c41a" />
              </div>
              <div style={{ marginBottom: 8 }}>
                <span>数据保密性</span>
                <Progress percent={82} status="active" strokeColor="#52c41a" />
              </div>
              <div style={{ marginBottom: 8 }}>
                <span>数据备份恢复</span>
                <Progress percent={70} status="active" strokeColor="#faad14" />
              </div>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="最近检查记录" style={{ height: 400 }}>
            <List
              dataSource={recentChecks}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={getStatusIcon(item.status)}
                    title={
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>{item.host}</span>
                        {getStatusTag(item.status)}
                      </div>
                    }
                    description={
                      <div>
                        <div>检查时间: {item.time}</div>
                        <div>合规得分: {item.score}分</div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
