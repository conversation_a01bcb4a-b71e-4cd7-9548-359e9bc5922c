import React, { useState } from 'react';
import { 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Tag, 
  Space, 
  Popconfirm,
  Card,
  Row,
  Col,
  message
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';

const { Option } = Select;

interface Host {
  id: string;
  name: string;
  ip: string;
  os: string;
  group: string;
  status: 'online' | 'offline' | 'unknown';
  lastCheck: string;
  complianceScore: number;
}

const HostManagement: React.FC = () => {
  const [hosts, setHosts] = useState<Host[]>([
    {
      id: '1',
      name: 'web-server-01',
      ip: '*************',
      os: 'Ubuntu 20.04',
      group: 'Web服务器',
      status: 'online',
      lastCheck: '2024-01-15 14:30:25',
      complianceScore: 85
    },
    {
      id: '2',
      name: 'db-server-01',
      ip: '*************',
      os: 'CentOS 7.9',
      group: '数据库服务器',
      status: 'online',
      lastCheck: '2024-01-15 14:25:10',
      complianceScore: 72
    },
    {
      id: '3',
      name: 'app-server-01',
      ip: '*************',
      os: 'Red Hat 8.5',
      group: '应用服务器',
      status: 'offline',
      lastCheck: '2024-01-15 14:20:45',
      complianceScore: 45
    }
  ]);

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingHost, setEditingHost] = useState<Host | null>(null);
  const [form] = Form.useForm();

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'online':
        return <Tag color="success" icon={<CheckCircleOutlined />}>在线</Tag>;
      case 'offline':
        return <Tag color="error" icon={<CloseCircleOutlined />}>离线</Tag>;
      case 'unknown':
        return <Tag color="warning" icon={<ExclamationCircleOutlined />}>未知</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  const getScoreTag = (score: number) => {
    if (score >= 80) {
      return <Tag color="success">{score}分</Tag>;
    } else if (score >= 60) {
      return <Tag color="warning">{score}分</Tag>;
    } else {
      return <Tag color="error">{score}分</Tag>;
    }
  };

  const columns = [
    {
      title: '主机名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
    },
    {
      title: '操作系统',
      dataIndex: 'os',
      key: 'os',
    },
    {
      title: '所属组',
      dataIndex: 'group',
      key: 'group',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '合规得分',
      dataIndex: 'complianceScore',
      key: 'complianceScore',
      render: (score: number) => getScoreTag(score),
    },
    {
      title: '最后检查',
      dataIndex: 'lastCheck',
      key: 'lastCheck',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Host) => (
        <Space size="middle">
          <Button 
            type="link" 
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个主机吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleAdd = () => {
    setEditingHost(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEdit = (host: Host) => {
    setEditingHost(host);
    form.setFieldsValue(host);
    setIsModalVisible(true);
  };

  const handleDelete = (id: string) => {
    setHosts(hosts.filter(host => host.id !== id));
    message.success('主机删除成功');
  };

  const handleOk = () => {
    form.validateFields().then(values => {
      if (editingHost) {
        setHosts(hosts.map(host => 
          host.id === editingHost.id ? { ...host, ...values } : host
        ));
        message.success('主机更新成功');
      } else {
        const newHost: Host = {
          id: Date.now().toString(),
          ...values,
          status: 'unknown',
          lastCheck: '-',
          complianceScore: 0
        };
        setHosts([...hosts, newHost]);
        message.success('主机添加成功');
      }
      setIsModalVisible(false);
    });
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  return (
    <div>
      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <h2>主机管理</h2>
          </Col>
          <Col>
            <Space>
              <Button icon={<ReloadOutlined />}>刷新状态</Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                添加主机
              </Button>
            </Space>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={hosts}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 台主机`,
          }}
        />
      </Card>

      <Modal
        title={editingHost ? '编辑主机' : '添加主机'}
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          name="hostForm"
        >
          <Form.Item
            name="name"
            label="主机名"
            rules={[{ required: true, message: '请输入主机名' }]}
          >
            <Input placeholder="请输入主机名" />
          </Form.Item>
          <Form.Item
            name="ip"
            label="IP地址"
            rules={[
              { required: true, message: '请输入IP地址' },
              { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: '请输入有效的IP地址' }
            ]}
          >
            <Input placeholder="请输入IP地址" />
          </Form.Item>
          <Form.Item
            name="os"
            label="操作系统"
            rules={[{ required: true, message: '请选择操作系统' }]}
          >
            <Select placeholder="请选择操作系统">
              <Option value="Ubuntu 20.04">Ubuntu 20.04</Option>
              <Option value="Ubuntu 22.04">Ubuntu 22.04</Option>
              <Option value="CentOS 7.9">CentOS 7.9</Option>
              <Option value="CentOS 8.5">CentOS 8.5</Option>
              <Option value="Red Hat 8.5">Red Hat 8.5</Option>
              <Option value="Red Hat 9.0">Red Hat 9.0</Option>
              <Option value="Debian 11">Debian 11</Option>
              <Option value="SUSE Linux">SUSE Linux</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="group"
            label="所属组"
            rules={[{ required: true, message: '请选择所属组' }]}
          >
            <Select placeholder="请选择所属组">
              <Option value="Web服务器">Web服务器</Option>
              <Option value="数据库服务器">数据库服务器</Option>
              <Option value="应用服务器">应用服务器</Option>
              <Option value="文件服务器">文件服务器</Option>
              <Option value="邮件服务器">邮件服务器</Option>
              <Option value="DNS服务器">DNS服务器</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default HostManagement;
