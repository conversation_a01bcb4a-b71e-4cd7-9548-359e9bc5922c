import React, { useState } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Select, 
  DatePicker, 
  Tag, 
  Space, 
  Row, 
  Col,
  Statistic,
  Progress,
  Modal,
  Descriptions,
  List,
  message,
  Tooltip
} from 'antd';
import { 
  DownloadOutlined, 
  EyeOutlined, 
  FileTextOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  Bar<PERSON>hartOutlined,
  FilePdfOutlined,
  FileExcelOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Option } = Select;
const { RangePicker } = DatePicker;

interface CheckResult {
  id: string;
  reportName: string;
  target: string;
  targetType: 'host' | 'group';
  template: string;
  checkTime: string;
  totalItems: number;
  passedItems: number;
  failedItems: number;
  warningItems: number;
  complianceScore: number;
  status: 'completed' | 'failed' | 'partial';
}

interface DetailResult {
  id: string;
  category: string;
  item: string;
  status: 'passed' | 'failed' | 'warning';
  description: string;
  suggestion: string;
}

const ResultGeneration: React.FC = () => {
  const [results, setResults] = useState<CheckResult[]>([
    {
      id: '1',
      reportName: 'Web服务器组-等保三级检查报告',
      target: 'Web服务器组',
      targetType: 'group',
      template: '等保三级Web服务器模板',
      checkTime: '2024-01-15 14:30:25',
      totalItems: 45,
      passedItems: 38,
      failedItems: 4,
      warningItems: 3,
      complianceScore: 85,
      status: 'completed'
    },
    {
      id: '2',
      reportName: 'db-server-01-等保三级检查报告',
      target: 'db-server-01',
      targetType: 'host',
      template: '等保三级数据库服务器模板',
      checkTime: '2024-01-15 14:25:10',
      totalItems: 52,
      passedItems: 37,
      failedItems: 8,
      warningItems: 7,
      complianceScore: 72,
      status: 'completed'
    },
    {
      id: '3',
      reportName: 'app-server-01-等保三级检查报告',
      target: 'app-server-01',
      targetType: 'host',
      template: '等保三级应用服务器模板',
      checkTime: '2024-01-15 14:20:45',
      totalItems: 48,
      passedItems: 22,
      failedItems: 18,
      warningItems: 8,
      complianceScore: 45,
      status: 'completed'
    }
  ]);

  const [selectedResult, setSelectedResult] = useState<CheckResult | null>(null);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null]>([null, null]);

  const detailResults: DetailResult[] = [
    {
      id: '1',
      category: '身份鉴别',
      item: '用户身份标识唯一性',
      status: 'passed',
      description: '系统用户身份标识唯一，无重复用户',
      suggestion: '继续保持当前配置'
    },
    {
      id: '2',
      category: '身份鉴别',
      item: '用户身份鉴别信息复杂度',
      status: 'failed',
      description: '密码复杂度策略不符合要求',
      suggestion: '建议设置密码最小长度为8位，包含大小写字母、数字和特殊字符'
    },
    {
      id: '3',
      category: '访问控制',
      item: '访问控制策略',
      status: 'warning',
      description: '部分用户权限过高',
      suggestion: '建议按照最小权限原则重新分配用户权限'
    }
  ];

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'completed':
        return <Tag color="success" icon={<CheckCircleOutlined />}>已完成</Tag>;
      case 'failed':
        return <Tag color="error" icon={<CloseCircleOutlined />}>失败</Tag>;
      case 'partial':
        return <Tag color="warning" icon={<ExclamationCircleOutlined />}>部分完成</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#faad14';
    return '#f5222d';
  };

  const getItemStatusTag = (status: string) => {
    switch (status) {
      case 'passed':
        return <Tag color="success">通过</Tag>;
      case 'failed':
        return <Tag color="error">失败</Tag>;
      case 'warning':
        return <Tag color="warning">警告</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  const columns = [
    {
      title: '报告名称',
      dataIndex: 'reportName',
      key: 'reportName',
      render: (text: string) => (
        <Space>
          <FileTextOutlined />
          {text}
        </Space>
      ),
    },
    {
      title: '检查目标',
      dataIndex: 'target',
      key: 'target',
      render: (text: string, record: CheckResult) => (
        <Space>
          {record.targetType === 'group' ? '📁' : '💻'}
          {text}
        </Space>
      ),
    },
    {
      title: '检查模板',
      dataIndex: 'template',
      key: 'template',
    },
    {
      title: '检查时间',
      dataIndex: 'checkTime',
      key: 'checkTime',
    },
    {
      title: '合规得分',
      dataIndex: 'complianceScore',
      key: 'complianceScore',
      render: (score: number) => (
        <Progress
          type="circle"
          size={50}
          percent={score}
          strokeColor={getScoreColor(score)}
          format={(percent) => `${percent}分`}
        />
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: CheckResult) => (
        <Space size="middle">
          <Tooltip title="查看详情">
            <Button 
              type="link" 
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            >
              查看
            </Button>
          </Tooltip>
          <Tooltip title="下载PDF报告">
            <Button 
              type="link" 
              icon={<FilePdfOutlined />}
              onClick={() => handleDownload(record, 'pdf')}
            >
              PDF
            </Button>
          </Tooltip>
          <Tooltip title="下载Excel报告">
            <Button 
              type="link" 
              icon={<FileExcelOutlined />}
              onClick={() => handleDownload(record, 'excel')}
            >
              Excel
            </Button>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const detailColumns = [
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '检查项',
      dataIndex: 'item',
      key: 'item',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getItemStatusTag(status),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '建议',
      dataIndex: 'suggestion',
      key: 'suggestion',
      ellipsis: true,
    },
  ];

  const handleViewDetail = (result: CheckResult) => {
    setSelectedResult(result);
    setIsDetailModalVisible(true);
  };

  const handleDownload = (result: CheckResult, format: 'pdf' | 'excel') => {
    message.success(`正在下载${format.toUpperCase()}报告: ${result.reportName}`);
    // 这里实现实际的下载逻辑
  };

  const filteredResults = results.filter(result => {
    if (filterStatus !== 'all' && result.status !== filterStatus) {
      return false;
    }
    
    if (dateRange.length === 2) {
      const checkTime = dayjs(result.checkTime);
      if (checkTime.isBefore(dateRange[0]) || checkTime.isAfter(dateRange[1])) {
        return false;
      }
    }
    
    return true;
  });

  const totalResults = filteredResults.length;
  const completedResults = filteredResults.filter(r => r.status === 'completed').length;
  const avgScore = filteredResults.length > 0 
    ? Math.round(filteredResults.reduce((sum, r) => sum + r.complianceScore, 0) / filteredResults.length)
    : 0;

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="报告总数"
              value={totalResults}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成"
              value={completedResults}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均得分"
              value={avgScore}
              suffix="分"
              prefix={<BarChartOutlined />}
              valueStyle={{ color: getScoreColor(avgScore) }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="合规率"
              value={avgScore}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: getScoreColor(avgScore) }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
          <Col>
            <h2>检查结果</h2>
          </Col>
          <Col>
            <Space>
              <Select
                style={{ width: 120 }}
                placeholder="状态筛选"
                value={filterStatus}
                onChange={setFilterStatus}
              >
                <Option value="all">全部</Option>
                <Option value="completed">已完成</Option>
                <Option value="failed">失败</Option>
                <Option value="partial">部分完成</Option>
              </Select>
              <RangePicker
                placeholder={['开始日期', '结束日期']}
                value={dateRange}
                onChange={(dates) => setDateRange(dates || [null, null])}
              />
              <Button icon={<DownloadOutlined />}>
                批量下载
              </Button>
            </Space>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={filteredResults}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个报告`,
          }}
        />
      </Card>

      <Modal
        title="检查结果详情"
        open={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={[
          <Button key="download-pdf" icon={<FilePdfOutlined />}>
            下载PDF
          </Button>,
          <Button key="download-excel" icon={<FileExcelOutlined />}>
            下载Excel
          </Button>,
          <Button key="close" onClick={() => setIsDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={1000}
      >
        {selectedResult && (
          <div>
            <Descriptions bordered column={2} style={{ marginBottom: 24 }}>
              <Descriptions.Item label="报告名称" span={2}>
                {selectedResult.reportName}
              </Descriptions.Item>
              <Descriptions.Item label="检查目标">
                {selectedResult.target}
              </Descriptions.Item>
              <Descriptions.Item label="检查模板">
                {selectedResult.template}
              </Descriptions.Item>
              <Descriptions.Item label="检查时间">
                {selectedResult.checkTime}
              </Descriptions.Item>
              <Descriptions.Item label="合规得分">
                <Tag color={getScoreColor(selectedResult.complianceScore)}>
                  {selectedResult.complianceScore}分
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="检查项统计" span={2}>
                <Space>
                  <Tag color="success">通过: {selectedResult.passedItems}</Tag>
                  <Tag color="error">失败: {selectedResult.failedItems}</Tag>
                  <Tag color="warning">警告: {selectedResult.warningItems}</Tag>
                  <Tag>总计: {selectedResult.totalItems}</Tag>
                </Space>
              </Descriptions.Item>
            </Descriptions>

            <h4>详细检查结果</h4>
            <Table
              columns={detailColumns}
              dataSource={detailResults}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ResultGeneration;
