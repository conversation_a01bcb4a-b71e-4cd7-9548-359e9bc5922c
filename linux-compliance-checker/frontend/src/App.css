/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 应用主容器 */
.ant-layout {
  min-height: 100vh;
}

/* 侧边栏样式 */
.ant-layout-sider {
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
}

/* 头部样式 */
.ant-layout-header {
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

/* 内容区域样式 */
.ant-layout-content {
  overflow: auto;
}

/* 卡片样式增强 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16),
              0 3px 6px 0 rgba(0, 0, 0, 0.12),
              0 5px 12px 4px rgba(0, 0, 0, 0.09);
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.ant-card-head-title {
  font-weight: 600;
  font-size: 16px;
}

/* 表格样式增强 */
.ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 按钮样式增强 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.4);
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
}

/* 统计卡片样式 */
.ant-statistic {
  text-align: center;
}

.ant-statistic-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 4px;
}

.ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 进度条样式 */
.ant-progress-circle {
  display: inline-block;
}

/* 标签样式增强 */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 模态框样式 */
.ant-modal {
  border-radius: 8px;
  overflow: hidden;
}

.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.ant-modal-title {
  font-weight: 600;
  font-size: 16px;
}

/* 步骤条样式 */
.ant-steps-item-title {
  font-weight: 500;
}

/* 列表样式 */
.ant-list-item {
  padding: 12px 0;
}

.ant-list-item-meta-title {
  font-weight: 500;
}

/* 描述列表样式 */
.ant-descriptions-item-label {
  font-weight: 600;
  background-color: #fafafa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed;
    height: 100vh;
    left: 0;
    z-index: 100;
  }

  .ant-layout-content {
    margin-left: 0;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
.ant-card,
.ant-btn,
.ant-table-row {
  transition: all 0.3s ease;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.online {
  background-color: #52c41a;
}

.status-indicator.offline {
  background-color: #f5222d;
}

.status-indicator.unknown {
  background-color: #faad14;
}